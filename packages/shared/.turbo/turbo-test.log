

> shared@0.1.0 test /Users/<USER>/CascadeProjects/ailex-receptionist/packages/shared
> jest

[1m[2mDetermining test suites to run...[22m[22m[999D[K

[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2m__tests__/[22m[1msanity.test.ts[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2m__tests__/[22m[1msanity.test.ts[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2m__tests__/[22m[1msanity.test.ts[22m

[K
[1A
[K
[1A
[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2m__tests__/[22m[1msanity.test.ts[22m

[K
[1A
[K
[1A[0m[7m[1m[32m PASS [39m[22m[27m[0m [2m__tests__/[22m[1msanity.test.ts[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2m__tests__/[22m[1msanity.test.ts[22m

[K
[1A
[K
[1A  Sanity Test

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2m__tests__/[22m[1msanity.test.ts[22m

[K
[1A
[K
[1A    [32m✓[39m [2mshould pass basic arithmetic test[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2m__tests__/[22m[1msanity.test.ts[22m

[K
[1A
[K
[1A    [32m✓[39m [2mshould handle string operations[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2m__tests__/[22m[1msanity.test.ts[22m

[K
[1A
[K
[1A    [32m✓[39m [2mshould handle array operations (1 ms)[22m

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2m__tests__/[22m[1msanity.test.ts[22m

[K
[1A
[K
[1A

[0m[7m[33m[1m RUNS [22m[39m[27m[0m [2m__tests__/[22m[1msanity.test.ts[22m

[K
[1A
[K
[1A[999D[K[1mTest Suites: [22m[1m[32m1 passed[39m[22m, 1 total
[1mTests:       [22m[1m[32m3 passed[39m[22m, 3 total
[1mSnapshots:   [22m0 total
[1mTime:[22m        0.329 s, estimated 1 s
[2mRan all test suites[22m[2m.[22m
